'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

const DEFAULT_DYNAMIC_LIST_ITEM_SIZE = 50;
const ITEM_RENDER_EVT = "itemRendered";
const SCROLL_EVT = "scroll";
const FORWARD = "forward";
const BACKWARD = "backward";
const AUTO_ALIGNMENT = "auto";
const SMART_ALIGNMENT = "smart";
const START_ALIGNMENT = "start";
const CENTERED_ALIGNMENT = "center";
const END_ALIGNMENT = "end";
const HORIZONTAL = "horizontal";
const VERTICAL = "vertical";
const LTR = "ltr";
const RTL = "rtl";
const RTL_OFFSET_NAG = "negative";
const RTL_OFFSET_POS_ASC = "positive-ascending";
const RTL_OFFSET_POS_DESC = "positive-descending";
const ScrollbarSizeKey = {
  [HORIZONTAL]: "height",
  [VERTICAL]: "width"
};
const ScrollbarDirKey = {
  [HORIZONTAL]: "left",
  [VERTICAL]: "top"
};
const SCROLLBAR_MIN_SIZE = 20;

exports.AUTO_ALIGNMENT = AUTO_ALIGNMENT;
exports.BACKWARD = BACKWARD;
exports.CENTERED_ALIGNMENT = CENTERED_ALIGNMENT;
exports.DEFAULT_DYNAMIC_LIST_ITEM_SIZE = DEFAULT_DYNAMIC_LIST_ITEM_SIZE;
exports.END_ALIGNMENT = END_ALIGNMENT;
exports.FORWARD = FORWARD;
exports.HORIZONTAL = HORIZONTAL;
exports.ITEM_RENDER_EVT = ITEM_RENDER_EVT;
exports.LTR = LTR;
exports.RTL = RTL;
exports.RTL_OFFSET_NAG = RTL_OFFSET_NAG;
exports.RTL_OFFSET_POS_ASC = RTL_OFFSET_POS_ASC;
exports.RTL_OFFSET_POS_DESC = RTL_OFFSET_POS_DESC;
exports.SCROLLBAR_MIN_SIZE = SCROLLBAR_MIN_SIZE;
exports.SCROLL_EVT = SCROLL_EVT;
exports.SMART_ALIGNMENT = SMART_ALIGNMENT;
exports.START_ALIGNMENT = START_ALIGNMENT;
exports.ScrollbarDirKey = ScrollbarDirKey;
exports.ScrollbarSizeKey = ScrollbarSizeKey;
exports.VERTICAL = VERTICAL;
//# sourceMappingURL=defaults.js.map
